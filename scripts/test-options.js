const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testOptions() {
  try {
    console.log('测试自定义选项功能...');

    // 检查表是否存在
    const { data, error } = await supabase
      .from('custom_options')
      .select('*')
      .limit(1);

    if (error) {
      console.error('custom_options 表不存在或无法访问:', error);
      console.log('请在 Supabase 控制台中执行 sql/create_custom_options.sql 文件');
      return;
    }

    console.log('custom_options 表存在，可以正常访问');
    console.log('当前表中的数据条数:', data ? data.length : 0);

    // 获取所有用户
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username');

    if (usersError) {
      console.error('获取用户失败:', usersError);
      return;
    }

    console.log('用户列表:');
    users.forEach(user => {
      console.log(`- ${user.username} (${user.id})`);
    });

    // 检查是否有班干职位选项
    const { data: classPositions, error: cpError } = await supabase
      .from('custom_options')
      .select('*')
      .eq('field_name', 'classPosition');

    if (cpError) {
      console.error('获取班干职位选项失败:', cpError);
      return;
    }

    console.log(`班干职位选项数量: ${classPositions.length}`);
    if (classPositions.length > 0) {
      console.log('班干职位选项:');
      classPositions.forEach(option => {
        console.log(`- ${option.option_value} (用户: ${option.user_id})`);
      });
    }

  } catch (error) {
    console.error('测试时出错:', error);
  }
}

testOptions();
