const { createClient } = require('@supabase/supabase-js');

// 加载环境变量
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  console.error('当前 SUPABASE_URL:', supabaseUrl);
  console.error('当前 SUPABASE_SERVICE_KEY:', supabaseServiceKey ? '已设置' : '未设置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addClassPositionOptions() {
  try {
    console.log('开始添加班干职位默认选项...');

    // 获取所有用户
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id');

    if (usersError) {
      console.error('获取用户列表失败:', usersError);
      return;
    }

    console.log(`找到 ${users.length} 个用户`);

    // 班干职位默认选项
    const classPositionOptions = [
      '无职位',
      '班长',
      '副班长',
      '学习委员',
      '体育委员',
      '文艺委员',
      '生活委员',
      '纪律委员',
      '宣传委员',
      '组织委员',
      '科代表',
      '小组长'
    ];

    // 为每个用户添加班干职位选项
    for (const user of users) {
      for (const option of classPositionOptions) {
        // 检查是否已存在
        const { data: existing } = await supabase
          .from('custom_options')
          .select('id')
          .eq('user_id', user.id)
          .eq('field_name', 'classPosition')
          .eq('option_value', option)
          .single();

        if (!existing) {
          const { error: insertError } = await supabase
            .from('custom_options')
            .insert({
              user_id: user.id,
              field_name: 'classPosition',
              option_value: option
            });

          if (insertError) {
            console.error(`为用户 ${user.id} 添加选项 "${option}" 失败:`, insertError);
          } else {
            console.log(`为用户 ${user.id} 添加选项 "${option}" 成功`);
          }
        } else {
          console.log(`用户 ${user.id} 已有选项 "${option}"`);
        }
      }
    }

    console.log('班干职位默认选项添加完成！');
  } catch (error) {
    console.error('添加班干职位选项时出错:', error);
  }
}

addClassPositionOptions();
