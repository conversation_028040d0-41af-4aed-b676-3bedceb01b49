const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请确保设置了 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createBatchTables() {
  try {
    console.log('开始创建批量生成相关表...');

    // 检查表是否已存在
    const { data: existingTasks } = await supabase
      .from('batch_generation_tasks')
      .select('id')
      .limit(1);

    if (existingTasks) {
      console.log('批量生成表已存在，跳过创建');
      return;
    }

    console.log('表不存在，需要手动在 Supabase 控制台创建');
    console.log('请在 Supabase 控制台的 SQL Editor 中执行以下 SQL:');
    console.log('=====================================');
    
    const sql = `
-- 批量生成任务表
CREATE TABLE IF NOT EXISTS batch_generation_tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_students INTEGER NOT NULL,
    completed_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 批量生成结果表
CREATE TABLE IF NOT EXISTS batch_generation_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    batch_task_id UUID REFERENCES batch_generation_tasks(id) ON DELETE CASCADE,
    student_name VARCHAR(100) NOT NULL,
    student_info JSONB NOT NULL,
    generated_comment TEXT,
    tokens_used INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'success', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_batch_tasks_user_id ON batch_generation_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_batch_tasks_status ON batch_generation_tasks(status);
CREATE INDEX IF NOT EXISTS idx_batch_tasks_created_at ON batch_generation_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_batch_results_task_id ON batch_generation_results(batch_task_id);
CREATE INDEX IF NOT EXISTS idx_batch_results_status ON batch_generation_results(status);
CREATE INDEX IF NOT EXISTS idx_batch_results_student_name ON batch_generation_results(student_name);

-- 启用行级安全策略 (RLS)
ALTER TABLE batch_generation_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_generation_results ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 用户只能查看自己的批量生成任务
CREATE POLICY "Users can view own batch tasks" ON batch_generation_tasks
    FOR SELECT USING (auth.uid() = user_id);

-- 服务可以插入和更新批量生成任务
CREATE POLICY "Service can manage batch tasks" ON batch_generation_tasks
    FOR ALL WITH CHECK (true);

-- 用户只能查看自己的批量生成结果
CREATE POLICY "Users can view own batch results" ON batch_generation_results
    FOR SELECT USING (
        batch_task_id IN (
            SELECT id FROM batch_generation_tasks WHERE auth.uid() = user_id
        )
    );

-- 服务可以插入和更新批量生成结果
CREATE POLICY "Service can manage batch results" ON batch_generation_results
    FOR ALL WITH CHECK (true);
`;

    console.log(sql);
    console.log('=====================================');

  } catch (error) {
    console.error('创建批量生成表时出错:', error);
  }
}

createBatchTables();
