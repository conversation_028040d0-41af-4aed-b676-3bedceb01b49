const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请确保设置了 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testBatchInsert() {
  try {
    console.log('测试批量生成表插入...');

    // 首先获取一个用户ID用于测试
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.error('无法获取用户数据:', usersError);
      return;
    }

    const userId = users[0].id;
    console.log('使用用户ID:', userId);

    // 测试插入批量任务
    const taskData = {
      user_id: userId,
      total_students: 2,
      completed_count: 0,
      status: 'pending'
    };

    console.log('尝试插入任务数据:', taskData);

    const { data: batchTask, error: taskError } = await supabase
      .from('batch_generation_tasks')
      .insert(taskData)
      .select()
      .single();

    if (taskError) {
      console.error('插入任务失败:', taskError);
      return;
    }

    console.log('任务插入成功:', batchTask);

    // 测试插入批量结果
    const resultData = {
      batch_task_id: batchTask.id,
      student_name: '测试学生',
      student_info: {
        studentName: '测试学生',
        studentGender: '男',
        classPosition: '无职位'
      },
      status: 'pending'
    };

    console.log('尝试插入结果数据:', resultData);

    const { data: batchResult, error: resultError } = await supabase
      .from('batch_generation_results')
      .insert(resultData)
      .select()
      .single();

    if (resultError) {
      console.error('插入结果失败:', resultError);
      return;
    }

    console.log('结果插入成功:', batchResult);

    // 清理测试数据
    await supabase
      .from('batch_generation_results')
      .delete()
      .eq('id', batchResult.id);

    await supabase
      .from('batch_generation_tasks')
      .delete()
      .eq('id', batchTask.id);

    console.log('测试完成，已清理测试数据');

  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

testBatchInsert();
