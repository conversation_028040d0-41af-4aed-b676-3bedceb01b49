const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请确保设置了 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixBatchTables() {
  try {
    console.log('开始修复批量生成表结构...');

    // 检查表是否存在
    const { data: existingTasks, error: checkError } = await supabase
      .from('batch_generation_tasks')
      .select('id')
      .limit(1);

    if (checkError) {
      console.log('表不存在，需要创建。请在 Supabase 控制台执行以下 SQL:');
    } else {
      console.log('表已存在，但可能需要修复结构。请在 Supabase 控制台执行以下 SQL:');
    }

    console.log('=====================================');
    
    const sql = `
-- 删除现有表（如果存在问题）
DROP TABLE IF EXISTS batch_generation_results CASCADE;
DROP TABLE IF EXISTS batch_generation_tasks CASCADE;

-- 重新创建批量生成任务表
CREATE TABLE batch_generation_tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    total_students INTEGER NOT NULL,
    completed_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 重新创建批量生成结果表
CREATE TABLE batch_generation_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    batch_task_id UUID NOT NULL REFERENCES batch_generation_tasks(id) ON DELETE CASCADE,
    student_name VARCHAR(100) NOT NULL,
    student_info JSONB NOT NULL,
    generated_comment TEXT,
    tokens_used INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'success', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_batch_tasks_user_id ON batch_generation_tasks(user_id);
CREATE INDEX idx_batch_tasks_status ON batch_generation_tasks(status);
CREATE INDEX idx_batch_tasks_created_at ON batch_generation_tasks(created_at);

CREATE INDEX idx_batch_results_task_id ON batch_generation_results(batch_task_id);
CREATE INDEX idx_batch_results_status ON batch_generation_results(status);
CREATE INDEX idx_batch_results_student_name ON batch_generation_results(student_name);

-- 启用行级安全策略 (RLS)
ALTER TABLE batch_generation_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_generation_results ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 用户只能查看自己的批量生成任务
CREATE POLICY "Users can view own batch tasks" ON batch_generation_tasks
    FOR SELECT USING (auth.uid() = user_id);

-- 服务可以插入和更新批量生成任务
CREATE POLICY "Service can manage batch tasks" ON batch_generation_tasks
    FOR ALL WITH CHECK (true);

-- 用户只能查看自己的批量生成结果
CREATE POLICY "Users can view own batch results" ON batch_generation_results
    FOR SELECT USING (
        batch_task_id IN (
            SELECT id FROM batch_generation_tasks WHERE auth.uid() = user_id
        )
    );

-- 服务可以插入和更新批量生成结果
CREATE POLICY "Service can manage batch results" ON batch_generation_results
    FOR ALL WITH CHECK (true);

-- 添加注释
COMMENT ON TABLE batch_generation_tasks IS '批量生成任务表';
COMMENT ON COLUMN batch_generation_tasks.user_id IS '用户ID';
COMMENT ON COLUMN batch_generation_tasks.total_students IS '总学生数';
COMMENT ON COLUMN batch_generation_tasks.completed_count IS '已完成数量';
COMMENT ON COLUMN batch_generation_tasks.status IS '任务状态：pending, processing, completed, failed';

COMMENT ON TABLE batch_generation_results IS '批量生成结果表';
COMMENT ON COLUMN batch_generation_results.batch_task_id IS '批量任务ID';
COMMENT ON COLUMN batch_generation_results.student_name IS '学生姓名';
COMMENT ON COLUMN batch_generation_results.student_info IS '学生信息JSON';
COMMENT ON COLUMN batch_generation_results.generated_comment IS '生成的评语';
COMMENT ON COLUMN batch_generation_results.tokens_used IS '使用的Token数量';
COMMENT ON COLUMN batch_generation_results.status IS '生成状态：pending, processing, success, failed';
COMMENT ON COLUMN batch_generation_results.error_message IS '错误信息';
`;

    console.log(sql);
    console.log('=====================================');
    console.log('\n执行完成后，批量生成功能应该可以正常工作。');

  } catch (error) {
    console.error('修复批量生成表时出错:', error);
  }
}

fixBatchTables();
