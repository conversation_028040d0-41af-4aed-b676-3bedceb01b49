const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createCustomOptionsTable() {
  try {
    console.log('开始创建 custom_options 表...');

    // 创建表的 SQL
    const createTableSQL = `
      -- 创建自定义选项表
      CREATE TABLE IF NOT EXISTS custom_options (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        field_name VARCHAR(100) NOT NULL,
        option_value TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, field_name, option_value)
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_custom_options_user_field ON custom_options(user_id, field_name);
      CREATE INDEX IF NOT EXISTS idx_custom_options_user_id ON custom_options(user_id);

      -- 添加注释
      COMMENT ON TABLE custom_options IS '用户自定义选项表';
      COMMENT ON COLUMN custom_options.user_id IS '用户ID';
      COMMENT ON COLUMN custom_options.field_name IS '字段名称';
      COMMENT ON COLUMN custom_options.option_value IS '选项值';
    `;

    // 使用 Supabase 的 SQL 执行功能
    const { data, error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('创建表失败:', error);
      console.log('尝试使用替代方法...');
      
      // 如果 exec_sql 不可用，尝试直接插入一条测试数据来触发表创建
      // 首先检查 users 表是否存在
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id')
        .limit(1);
      
      if (usersError) {
        console.error('无法访问 users 表:', usersError);
        console.log('\n请手动在 Supabase 控制台中执行以下 SQL:');
        console.log(createTableSQL);
        return;
      }
      
      console.log('users 表存在，但无法直接创建 custom_options 表');
      console.log('\n请在 Supabase 控制台的 SQL Editor 中执行以下 SQL:');
      console.log('=====================================');
      console.log(createTableSQL);
      console.log('=====================================');
      return;
    }

    console.log('表创建成功！');

    // 获取所有用户
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username');

    if (usersError) {
      console.error('获取用户列表失败:', usersError);
      return;
    }

    console.log(`找到 ${users.length} 个用户，开始添加默认选项...`);

    // 所有字段的默认选项
    const defaultOptions = {
      'overallAssessment': [
        '该生表现优秀，各方面发展均衡',
        '该生学习态度端正，成绩稳步提升',
        '该生积极向上，具有良好的发展潜力',
        '该生在某些方面表现突出，需继续保持',
        '该生基础扎实，但需要在某些方面加强努力'
      ],
      'futureExpectations': [
        '希望能在学习上更加主动积极',
        '期待在各学科均衡发展',
        '希望能发挥特长，全面发展',
        '期望能提高自主学习能力',
        '希望能在团队合作中发挥更大作用'
      ],
      'improvementSuggestions': [
        '建议加强基础知识的巩固练习',
        '建议多参与课堂讨论，提高表达能力',
        '建议制定合理的学习计划并坚持执行',
        '建议多阅读课外书籍，拓宽知识面',
        '建议积极参与集体活动，培养团队精神'
      ],
      'subjectStrengths': [
        '语文阅读理解能力强',
        '数学逻辑思维清晰',
        '英语口语表达流利',
        '理科思维敏捷',
        '文科知识面广',
        '艺术天赋突出',
        '体育运动能力强'
      ],
      'subjectWeaknesses': [
        '数学计算准确性有待提高',
        '语文写作表达需要加强',
        '英语词汇量需要扩大',
        '理科实验操作需要练习',
        '文科记忆背诵需要加强',
        '注意力集中度需要提升',
        '学习方法需要改进'
      ],
      'classPosition': [
        '无职位',
        '班长',
        '副班长',
        '学习委员',
        '体育委员',
        '文艺委员',
        '生活委员',
        '纪律委员',
        '宣传委员',
        '组织委员',
        '科代表',
        '小组长'
      ]
    };

    // 为每个用户添加所有字段的默认选项
    let successCount = 0;
    let errorCount = 0;

    for (const user of users) {
      for (const [fieldName, options] of Object.entries(defaultOptions)) {
        for (const option of options) {
          try {
            const { error: insertError } = await supabase
              .from('custom_options')
              .insert({
                user_id: user.id,
                field_name: fieldName,
                option_value: option
              });

            if (insertError) {
              // 忽略重复数据错误
              if (!insertError.message.includes('duplicate') && !insertError.message.includes('unique')) {
                console.error(`为用户 ${user.username} 添加 ${fieldName} 选项 "${option}" 失败:`, insertError);
                errorCount++;
              }
            } else {
              successCount++;
            }
          } catch (error) {
            console.error(`为用户 ${user.username} 添加 ${fieldName} 选项 "${option}" 时出错:`, error);
            errorCount++;
          }
        }
      }
    }

    console.log(`\n默认选项添加完成！`);
    console.log(`成功添加: ${successCount} 条`);
    console.log(`错误/重复: ${errorCount} 条`);
    console.log('\ncustom_options 表创建和初始化完成！');

  } catch (error) {
    console.error('创建表时出错:', error);
  }
}

createCustomOptionsTable();
