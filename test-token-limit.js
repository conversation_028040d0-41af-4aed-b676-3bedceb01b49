// 测试 Token 限制功能的脚本
// 使用方法: node test-token-limit.js

const testTokenLimit = async () => {
  const baseUrl = 'http://localhost:3000'; // 根据您的实际端口调整
  
  // 测试用户登录信息（请根据实际情况修改）
  const testUser = {
    username: 'test2', // 这是您设置了 token_limit = 1 的用户
    password: 'your_password_here' // 请填入实际密码
  };

  try {
    console.log('🔐 正在登录测试用户...');
    
    // 1. 登录获取 token
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    if (!loginResponse.ok) {
      throw new Error(`登录失败: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const userToken = loginData.token;
    console.log('✅ 登录成功');

    // 2. 准备测试数据
    const testStudentInfo = {
      studentName: "测试学生",
      studentGender: "男",
      subjectStrengths: "数学、物理较强",
      subjectWeaknesses: "语文写作需要提高",
      learningPotential: "潜力较大",
      subjectInterest: "浓厚",
      classroomConcentration: "大部分时间专注",
      homeworkCompletion: "总是按时高质量",
      learningProactiveness: "学习主动性强",
      disciplineCompliance: "严格遵守",
      attitudeTowardsOthers: "有礼貌尊重他人",
      responsibility: "较强",
      talentsAndInterests: "喜欢编程和数学",
      classPosition: "学习委员",
      awards: "数学竞赛二等奖",
      overallAssessment: "该生表现优秀，各方面发展均衡",
      futureExpectations: "希望能在学习上更加主动积极",
      improvementSuggestions: "建议加强基础知识的巩固练习",
      commentPerspective: "你",
      commentTone: "温和亲切",
      wordCountRange: "100-150字"
    };

    console.log('🧪 开始测试 Token 限制功能...');

    // 3. 尝试生成评语（应该会被 token 限制阻止）
    const generateResponse = await fetch(`${baseUrl}/api/generate-comment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({
        studentInfo: testStudentInfo
      })
    });

    const generateData = await generateResponse.json();
    
    console.log('📊 生成评语响应:');
    console.log('状态码:', generateResponse.status);
    console.log('响应数据:', JSON.stringify(generateData, null, 2));

    if (generateResponse.status === 403) {
      console.log('✅ Token 限制功能正常工作！用户被正确阻止。');
    } else if (generateData.success) {
      console.log('⚠️  Token 限制可能没有生效，评语生成成功了。');
      console.log('使用的 Token 数量:', generateData.tokensUsed);
    } else {
      console.log('❌ 出现了其他错误:', generateData.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
};

// 运行测试
testTokenLimit();
