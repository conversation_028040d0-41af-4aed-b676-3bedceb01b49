'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User, AdminStats, UserTokenStats, AdminCreateUserRequest } from '@/types';

export default function AdminPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userTokenStats, setUserTokenStats] = useState<UserTokenStats | null>(null);
  const [showTokenModal, setShowTokenModal] = useState(false);
  const [tokenLimit, setTokenLimit] = useState<string>('');

  // 创建用户相关状态
  const [showCreateUserModal, setShowCreateUserModal] = useState(false);
  const [createUserForm, setCreateUserForm] = useState<AdminCreateUserRequest>({
    username: '',
    email: '',
    password: '',
    is_active: true,
    token_limit: null
  });
  const [createUserLoading, setCreateUserLoading] = useState(false);

  // 检查管理员权限
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (!parsedUser.is_admin) {
        router.push('/');
        return;
      }
      setUser(parsedUser);
      loadData();
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/login');
    }
  }, [router]);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      
      // 并行加载用户列表和统计数据
      const [usersResponse, statsResponse] = await Promise.all([
        fetch('/api/admin/users', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/admin/stats', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setUsers(usersData.data || []);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      setError('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换用户状态
  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userId,
          is_active: !currentStatus
        })
      });

      const data = await response.json();
      if (data.success) {
        // 更新本地状态
        setUsers(prev => prev.map(user => 
          user.id === userId 
            ? { ...user, is_active: !currentStatus }
            : user
        ));
      } else {
        alert(data.message || '操作失败');
      }
    } catch (error) {
      console.error('切换用户状态失败:', error);
      alert('操作失败，请稍后重试');
    }
  };

  // 删除用户
  const deleteUser = async (userId: string, username: string) => {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users?userId=${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        // 从列表中移除用户
        setUsers(prev => prev.filter(user => user.id !== userId));
        // 重新加载统计数据
        loadData();
      } else {
        alert(data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      alert('删除失败，请稍后重试');
    }
  };

  // 查看用户Token使用情况
  const viewUserTokens = async (user: User) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users/${user.id}/tokens`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setUserTokenStats(data.data);
        setSelectedUser(user);
        setShowTokenModal(true);
      } else {
        alert('获取Token使用情况失败');
      }
    } catch (error) {
      console.error('获取Token使用情况失败:', error);
      alert('获取Token使用情况失败');
    }
  };

  // 设置Token限制
  const setUserTokenLimit = async () => {
    if (!selectedUser) return;

    try {
      const token = localStorage.getItem('token');
      const limit = tokenLimit === '' ? null : parseInt(tokenLimit);

      if (tokenLimit !== '' && (isNaN(limit!) || limit! < 0)) {
        alert('请输入有效的Token限制数量');
        return;
      }

      const response = await fetch(`/api/admin/users/${selectedUser.id}/token-limit`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ tokenLimit: limit })
      });

      const data = await response.json();
      if (data.success) {
        alert(data.message);
        // 更新用户列表中的token_limit
        setUsers(prev => prev.map(user =>
          user.id === selectedUser.id
            ? { ...user, token_limit: limit }
            : user
        ));
        // 刷新Token统计
        if (userTokenStats) {
          viewUserTokens(selectedUser);
        }
      } else {
        alert(data.message || '设置失败');
      }
    } catch (error) {
      console.error('设置Token限制失败:', error);
      alert('设置Token限制失败');
    }
  };

  // 关闭Token模态框
  const closeTokenModal = () => {
    setShowTokenModal(false);
    setSelectedUser(null);
    setUserTokenStats(null);
    setTokenLimit('');
  };

  // 创建用户
  const createUser = async () => {
    // 验证表单
    if (!createUserForm.username || !createUserForm.email || !createUserForm.password) {
      alert('请填写所有必填字段');
      return;
    }

    if (createUserForm.username.length < 3) {
      alert('用户名长度不能少于3个字符');
      return;
    }

    if (createUserForm.password.length < 6) {
      alert('密码长度不能少于6个字符');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(createUserForm.email)) {
      alert('请输入有效的邮箱地址');
      return;
    }

    setCreateUserLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(createUserForm)
      });

      const data = await response.json();
      if (data.success) {
        alert('用户创建成功');
        // 重置表单
        setCreateUserForm({
          username: '',
          email: '',
          password: '',
          is_active: true,
          token_limit: null
        });
        setShowCreateUserModal(false);
        // 重新加载数据
        loadData();
      } else {
        alert(data.message || '创建用户失败');
      }
    } catch (error) {
      console.error('创建用户失败:', error);
      alert('创建用户失败，请稍后重试');
    } finally {
      setCreateUserLoading(false);
    }
  };

  // 关闭创建用户模态框
  const closeCreateUserModal = () => {
    setShowCreateUserModal(false);
    setCreateUserForm({
      username: '',
      email: '',
      password: '',
      is_active: true,
      token_limit: null
    });
  };

  // 退出登录
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">管理员后台</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">管理员：{user.username}</span>
              <button
                onClick={() => router.push('/')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                返回主页
              </button>
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-blue-600">{stats.totalUsers}</div>
              <div className="text-sm text-gray-600">总用户数</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-green-600">{stats.activeUsers}</div>
              <div className="text-sm text-gray-600">活跃用户</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-purple-600">{stats.totalComments}</div>
              <div className="text-sm text-gray-600">生成评语数</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-orange-600">{stats.totalTokensUsed}</div>
              <div className="text-sm text-gray-600">总Token使用量</div>
            </div>
          </div>
        )}

        {/* 用户管理表格 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">用户管理</h2>
            <button
              onClick={() => setShowCreateUserModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              创建用户
            </button>
          </div>
          
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div className="mt-2 text-gray-600">加载中...</div>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-600">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Token限制
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      注册时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      最后登录
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.username}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.is_active ? '活跃' : '禁用'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.token_limit ? (
                          <span className="text-orange-600 font-medium">
                            {user.token_limit.toLocaleString()}
                          </span>
                        ) : (
                          <span className="text-gray-400">无限制</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(user.created_at).toLocaleDateString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.last_login 
                          ? new Date(user.last_login).toLocaleDateString('zh-CN')
                          : '从未登录'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-col space-y-1">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => viewUserTokens(user)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Token详情
                            </button>
                            <button
                              onClick={() => toggleUserStatus(user.id, user.is_active)}
                              className={`${
                                user.is_active
                                  ? 'text-red-600 hover:text-red-900'
                                  : 'text-green-600 hover:text-green-900'
                              }`}
                            >
                              {user.is_active ? '禁用' : '启用'}
                            </button>
                          </div>
                          <button
                            onClick={() => deleteUser(user.id, user.username)}
                            className="text-red-600 hover:text-red-900 text-left"
                          >
                            删除用户
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {users.length === 0 && (
                <div className="p-6 text-center text-gray-500">暂无用户数据</div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Token详情模态框 */}
      {showTokenModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedUser.username} 的Token使用详情
              </h3>
              <button
                onClick={closeTokenModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {userTokenStats && (
              <div className="space-y-6">
                {/* Token使用概览 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600">
                      {userTokenStats.total_tokens_used.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">总使用量</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-orange-600">
                      {userTokenStats.token_limit?.toLocaleString() || '无限制'}
                    </div>
                    <div className="text-sm text-gray-600">使用限制</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-purple-600">
                      {userTokenStats.usage_percentage}%
                    </div>
                    <div className="text-sm text-gray-600">使用率</div>
                  </div>
                </div>

                {/* 使用率进度条 */}
                {userTokenStats.token_limit && (
                  <div className="bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full ${
                        userTokenStats.usage_percentage >= 90 ? 'bg-red-500' :
                        userTokenStats.usage_percentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(userTokenStats.usage_percentage, 100)}%` }}
                    ></div>
                  </div>
                )}

                {/* Token限制设置 */}
                <div className="border-t pt-4">
                  <h4 className="text-md font-medium text-gray-900 mb-3">设置Token限制</h4>
                  <div className="flex items-center space-x-3">
                    <input
                      type="number"
                      value={tokenLimit}
                      onChange={(e) => setTokenLimit(e.target.value)}
                      placeholder={userTokenStats.token_limit?.toString() || "输入限制数量（留空表示无限制）"}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                      onClick={setUserTokenLimit}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      设置限制
                    </button>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    留空表示无限制，设置为0表示禁止使用
                  </p>
                </div>

                {/* 每日使用趋势 */}
                <div className="border-t pt-4">
                  <h4 className="text-md font-medium text-gray-900 mb-3">最近7天使用趋势</h4>
                  <div className="grid grid-cols-7 gap-2">
                    {userTokenStats.daily_usage.map((day, index) => (
                      <div key={index} className="text-center">
                        <div className="text-xs text-gray-500 mb-1">
                          {new Date(day.date).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}
                        </div>
                        <div className="bg-blue-100 rounded p-2">
                          <div className="text-sm font-medium text-blue-800">
                            {day.tokens.toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 最近使用记录 */}
                <div className="border-t pt-4">
                  <h4 className="text-md font-medium text-gray-900 mb-3">最近使用记录</h4>
                  <div className="max-h-60 overflow-y-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                            时间
                          </th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                            类型
                          </th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                            Token数量
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {userTokenStats.recent_usage.map((usage, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {new Date(usage.created_at).toLocaleString('zh-CN')}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-500">
                              {usage.api_call_type}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {usage.tokens_used.toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {userTokenStats.recent_usage.length === 0 && (
                      <div className="text-center py-4 text-gray-500">暂无使用记录</div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 创建用户模态框 */}
      {showCreateUserModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">创建新用户</h3>
              <button
                onClick={closeCreateUserModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={(e) => { e.preventDefault(); createUser(); }} className="space-y-4">
              {/* 用户名 */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  用户名 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="username"
                  value={createUserForm.username}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入用户名（3-50个字符）"
                  required
                />
              </div>

              {/* 邮箱 */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱 <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  value={createUserForm.email}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入邮箱地址"
                  required
                />
              </div>

              {/* 密码 */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  id="password"
                  value={createUserForm.password}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入密码（至少6个字符）"
                  required
                />
              </div>

              {/* 账号状态 */}
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={createUserForm.is_active}
                    onChange={(e) => setCreateUserForm(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">启用账号</span>
                </label>
              </div>

              {/* Token限制 */}
              <div>
                <label htmlFor="tokenLimit" className="block text-sm font-medium text-gray-700 mb-1">
                  Token使用限制
                </label>
                <input
                  type="number"
                  id="tokenLimit"
                  value={createUserForm.token_limit || ''}
                  onChange={(e) => setCreateUserForm(prev => ({
                    ...prev,
                    token_limit: e.target.value === '' ? null : parseInt(e.target.value)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="留空表示无限制"
                  min="0"
                />
                <p className="text-xs text-gray-500 mt-1">留空表示无限制，设置为0表示禁止使用</p>
              </div>

              {/* 按钮 */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={closeCreateUserModal}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                  disabled={createUserLoading}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  disabled={createUserLoading}
                >
                  {createUserLoading ? '创建中...' : '创建用户'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
