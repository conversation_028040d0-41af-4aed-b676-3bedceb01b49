'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BatchGenerationStatus, User } from '@/types';

export default function BatchStatusPage({ params }: { params: { batchId: string } }) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [status, setStatus] = useState<BatchGenerationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 检查用户登录状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/login');
    }
  }, [router]);

  // 获取批量生成状态
  const fetchStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/batch-generation-status/${params.batchId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setStatus(data);
        setError(null);
      } else {
        setError(data.message || '获取状态失败');
      }
    } catch (error) {
      console.error('获取批量生成状态错误:', error);
      setError('获取状态失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 定期刷新状态
  useEffect(() => {
    if (!user) return;

    fetchStatus();

    // 如果任务还在进行中，每3秒刷新一次状态
    const interval = setInterval(() => {
      if (status?.task.status === 'processing' || status?.task.status === 'pending') {
        fetchStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [user, params.batchId]);

  // 计算进度百分比
  const getProgress = () => {
    if (!status) return 0;
    return Math.round((status.task.completed_count / status.task.total_students) * 100);
  };

  // 获取状态显示文本
  const getStatusText = () => {
    if (!status) return '';
    
    switch (status.task.status) {
      case 'pending':
        return '等待开始';
      case 'processing':
        return '生成中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '生成失败';
      default:
        return '未知状态';
    }
  };

  // 获取状态颜色
  const getStatusColor = () => {
    if (!status) return 'text-gray-500';
    
    switch (status.task.status) {
      case 'pending':
        return 'text-yellow-600';
      case 'processing':
        return 'text-blue-600';
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  // 复制所有评语
  const copyAllComments = () => {
    if (!status) return;
    
    const successResults = status.results.filter(result => result.status === 'success');
    const allComments = successResults.map(result => 
      `${result.student_name}：\n${result.generated_comment}\n`
    ).join('\n---\n\n');
    
    navigator.clipboard.writeText(allComments);
    alert('所有评语已复制到剪贴板');
  };

  // 退出登录
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div>加载批量生成状态...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <button
            onClick={() => router.push('/batch')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            返回批量生成
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">批量生成状态</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">欢迎，{user.username}</span>
              <button
                onClick={() => router.push('/batch')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                新建批量生成
              </button>
              <button
                onClick={() => router.push('/')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                单个生成
              </button>
              <button
                onClick={() => router.push('/history')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                历史评语
              </button>
              {user.is_admin && (
                <button
                  onClick={() => router.push('/admin')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  管理后台
                </button>
              )}
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {status && (
          <>
            {/* 状态概览 */}
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">生成状态</h2>
                <span className={`text-sm font-medium ${getStatusColor()}`}>
                  {getStatusText()}
                </span>
              </div>

              {/* 进度条 */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>进度</span>
                  <span>{status.task.completed_count} / {status.task.total_students}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgress()}%` }}
                  ></div>
                </div>
                <div className="text-center text-sm text-gray-600 mt-1">
                  {getProgress()}%
                </div>
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{status.task.total_students}</div>
                  <div className="text-sm text-gray-600">总学生数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {status.results.filter(r => r.status === 'success').length}
                  </div>
                  <div className="text-sm text-gray-600">成功生成</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {status.results.filter(r => r.status === 'failed').length}
                  </div>
                  <div className="text-sm text-gray-600">生成失败</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{status.totalTokensUsed}</div>
                  <div className="text-sm text-gray-600">总Token使用</div>
                </div>
              </div>

              {/* 操作按钮 */}
              {status.task.status === 'completed' && (
                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={copyAllComments}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    复制所有评语
                  </button>
                </div>
              )}
            </div>

            {/* 详细结果 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">详细结果</h3>
              </div>

              <div className="divide-y divide-gray-200">
                {status.results.map((result, index) => (
                  <div key={result.id} className="p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="text-md font-medium text-gray-900">
                        {result.student_name}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          result.status === 'success' ? 'bg-green-100 text-green-800' :
                          result.status === 'failed' ? 'bg-red-100 text-red-800' :
                          result.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {result.status === 'success' ? '成功' :
                           result.status === 'failed' ? '失败' :
                           result.status === 'processing' ? '生成中' : '等待中'}
                        </span>
                        {result.tokens_used > 0 && (
                          <span className="text-xs text-gray-500">
                            {result.tokens_used} tokens
                          </span>
                        )}
                      </div>
                    </div>

                    {result.status === 'success' && result.generated_comment && (
                      <div className="mt-3">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="whitespace-pre-wrap text-gray-800 leading-relaxed text-sm">
                            {result.generated_comment}
                          </div>
                        </div>
                        <div className="mt-2">
                          <button
                            onClick={() => navigator.clipboard.writeText(result.generated_comment!)}
                            className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                          >
                            复制评语
                          </button>
                        </div>
                      </div>
                    )}

                    {result.status === 'failed' && result.error_message && (
                      <div className="mt-3">
                        <div className="bg-red-50 rounded-lg p-4">
                          <div className="text-red-800 text-sm">
                            错误：{result.error_message}
                          </div>
                        </div>
                      </div>
                    )}

                    {result.status === 'processing' && (
                      <div className="mt-3">
                        <div className="flex items-center text-blue-600 text-sm">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          正在生成评语...
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
