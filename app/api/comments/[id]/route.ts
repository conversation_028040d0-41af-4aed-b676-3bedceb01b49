import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { CommentRecord } from '@/types';

interface CommentDetailResponse {
  success: boolean;
  message: string;
  comment?: CommentRecord;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json<CommentDetailResponse>({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json<CommentDetailResponse>({
        success: false,
        message: '认证令牌无效或已过期'
      }, { status: 401 });
    }

    const { id } = await params;

    // 查询评语详情，确保只能查看自己的评语
    const { data: comment, error } = await supabaseAdmin
      .from('comments')
      .select('*')
      .eq('id', id)
      .eq('user_id', payload.userId)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json<CommentDetailResponse>({
        success: false,
        message: '评语不存在或无权访问'
      }, { status: 404 });
    }

    return NextResponse.json<CommentDetailResponse>({
      success: true,
      message: '查询成功',
      comment: comment as CommentRecord
    });

  } catch (error) {
    console.error('Comment detail query error:', error);
    return NextResponse.json<CommentDetailResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
