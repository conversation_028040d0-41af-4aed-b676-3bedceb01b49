import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { CommentsQueryResponse, CommentRecord } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json<CommentsQueryResponse>({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json<CommentsQueryResponse>({
        success: false,
        message: '认证令牌无效或已过期'
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const studentName = searchParams.get('studentName') || '';
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';

    // 构建查询条件
    let query = supabaseAdmin
      .from('comments')
      .select('*', { count: 'exact' })
      .eq('user_id', payload.userId)
      .order('created_at', { ascending: false });

    // 添加学生姓名筛选
    if (studentName) {
      query = query.ilike('student_name', `%${studentName}%`);
    }

    // 添加日期范围筛选
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    // 添加分页
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: comments, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json<CommentsQueryResponse>({
        success: false,
        message: '查询评语记录失败'
      }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json<CommentsQueryResponse>({
      success: true,
      message: '查询成功',
      comments: comments as CommentRecord[],
      total: count || 0,
      page,
      limit,
      totalPages
    });

  } catch (error) {
    console.error('Comments query error:', error);
    return NextResponse.json<CommentsQueryResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
