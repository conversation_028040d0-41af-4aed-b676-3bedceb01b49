import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { hashPassword } from '@/lib/auth';
import { RegisterRequest, AuthResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json();
    const { username, email, password } = body;

    // 验证输入
    if (!username || !email || !password) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名、邮箱和密码不能为空'
      }, { status: 400 });
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 20) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名长度必须在3-20个字符之间'
      }, { status: 400 });
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '密码长度至少6个字符'
      }, { status: 400 });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '邮箱格式不正确'
      }, { status: 400 });
    }

    // 检查用户名是否已存在
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('username')
      .eq('username', username)
      .single();

    if (existingUser) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名已存在'
      }, { status: 409 });
    }

    // 检查邮箱是否已存在
    const { data: existingEmail } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('email', email)
      .single();

    if (existingEmail) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '邮箱已被注册'
      }, { status: 409 });
    }

    // 加密密码
    const passwordHash = await hashPassword(password);

    // 创建新用户
    const { data: newUser, error } = await supabaseAdmin
      .from('users')
      .insert({
        username,
        email,
        password_hash: passwordHash,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id, username, email, is_active, created_at, updated_at')
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '注册失败，请稍后重试'
      }, { status: 500 });
    }

    // 为返回的用户数据添加is_admin字段
    const userWithAdmin = { ...newUser, is_admin: false };

    return NextResponse.json<AuthResponse>({
      success: true,
      message: '注册成功，请登录',
      user: userWithAdmin
    }, { status: 201 });

  } catch (error) {
    console.error('Register error:', error);
    return NextResponse.json<AuthResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
