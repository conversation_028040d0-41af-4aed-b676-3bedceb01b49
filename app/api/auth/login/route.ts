import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { generateToken, verifyPassword, verifyAdminCredentials } from '@/lib/auth';
import { LoginRequest, AuthResponse, User } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    const { username, password } = body;

    // 验证输入
    if (!username || !password) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名和密码不能为空'
      }, { status: 400 });
    }

    // 检查是否为管理员登录
    if (verifyAdminCredentials(username, password)) {
      // 管理员登录成功
      const adminUser: User = {
        id: 'admin',
        username: username,
        email: '<EMAIL>',
        is_active: true,
        is_admin: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const token = generateToken(adminUser);

      return NextResponse.json<AuthResponse>({
        success: true,
        message: '管理员登录成功',
        token,
        user: adminUser
      });
    }

    // 普通用户登录 - 从数据库查询用户
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, username, email, password_hash, is_active, created_at, updated_at, last_login')
      .eq('username', username)
      .single();

    if (error || !user) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名或密码错误'
      }, { status: 401 });
    }

    // 检查用户是否被禁用
    if (!user.is_active) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '账号已被禁用，请联系管理员'
      }, { status: 403 });
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名或密码错误'
      }, { status: 401 });
    }

    // 为普通用户添加is_admin字段（默认为false）
    const userWithAdmin = { ...user, is_admin: false };

    // 生成JWT Token
    const token = generateToken(userWithAdmin);

    // 更新最后登录时间
    await supabaseAdmin
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id);

    // 返回成功响应（不包含密码）
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password_hash: _password_hash, ...userWithoutPassword } = userWithAdmin;

    return NextResponse.json<AuthResponse>({
      success: true,
      message: '登录成功',
      token,
      user: userWithoutPassword
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json<AuthResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
