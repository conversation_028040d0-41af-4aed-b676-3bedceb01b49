import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { AdminStats } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    // 获取统计数据
    const [usersResult, activeUsersResult, tokenUsageResult] = await Promise.all([
      // 总用户数
      supabaseAdmin
        .from('users')
        .select('id', { count: 'exact', head: true }),

      // 活跃用户数
      supabaseAdmin
        .from('users')
        .select('id', { count: 'exact', head: true })
        .eq('is_active', true),

      // 总Token使用量
      supabaseAdmin
        .from('token_usage')
        .select('tokens_used')
    ]);

    // 计算总Token使用量
    const totalTokensUsed = tokenUsageResult.data?.reduce((sum, record) => sum + record.tokens_used, 0) || 0;

    const stats: AdminStats = {
      totalUsers: usersResult.count || 0,
      activeUsers: activeUsersResult.count || 0,
      totalTokensUsed,
      totalComments: 0 // TODO: 等comments表创建后再统计
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get admin stats error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
