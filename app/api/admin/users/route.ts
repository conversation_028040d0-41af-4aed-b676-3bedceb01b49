import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader, hashPassword } from '@/lib/auth';
import { AdminCreateUserRequest, AuthResponse } from '@/types';

// 获取用户列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    // 获取用户列表
    const { data: users, error } = await supabaseAdmin
      .from('users')
      .select('id, username, email, is_active, token_limit, created_at, updated_at, last_login')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '获取用户列表失败'
      }, { status: 500 });
    }

    // 为所有用户添加is_admin字段（默认为false）
    const usersWithAdmin = users?.map(user => ({ ...user, is_admin: false })) || [];

    return NextResponse.json({
      success: true,
      data: usersWithAdmin
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 更新用户状态
export async function PATCH(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const body = await request.json();
    const { userId, is_active } = body;

    if (!userId || typeof is_active !== 'boolean') {
      return NextResponse.json({
        success: false,
        message: '参数不正确'
      }, { status: 400 });
    }

    // 更新用户状态
    const { error } = await supabaseAdmin
      .from('users')
      .update({ 
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '更新用户状态失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `用户已${is_active ? '启用' : '禁用'}`
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 删除用户
export async function DELETE(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '用户ID不能为空'
      }, { status: 400 });
    }

    // 删除用户相关的所有数据
    await supabaseAdmin.from('token_usage').delete().eq('user_id', userId);
    await supabaseAdmin.from('comments').delete().eq('user_id', userId);
    
    // 删除用户
    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '删除用户失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '用户已删除'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 创建新用户
export async function POST(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    // 解析请求体
    const body: AdminCreateUserRequest = await request.json();
    const { username, email, password, is_active = true, token_limit = null } = body;

    // 验证必填字段
    if (!username || !email || !password) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名、邮箱和密码不能为空'
      }, { status: 400 });
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 50) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名长度必须在3-50个字符之间'
      }, { status: 400 });
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '密码长度不能少于6个字符'
      }, { status: 400 });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '邮箱格式不正确'
      }, { status: 400 });
    }

    // 验证token_limit
    if (token_limit !== null && (typeof token_limit !== 'number' || token_limit < 0)) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: 'Token限制必须是非负数或null'
      }, { status: 400 });
    }

    // 检查用户名是否已存在
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('username')
      .eq('username', username)
      .single();

    if (existingUser) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '用户名已存在'
      }, { status: 409 });
    }

    // 检查邮箱是否已存在
    const { data: existingEmail } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('email', email)
      .single();

    if (existingEmail) {
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '邮箱已被注册'
      }, { status: 409 });
    }

    // 加密密码
    const passwordHash = await hashPassword(password);

    // 创建新用户
    const { data: newUser, error } = await supabaseAdmin
      .from('users')
      .insert({
        username,
        email,
        password_hash: passwordHash,
        is_active,
        token_limit,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id, username, email, is_active, token_limit, created_at, updated_at')
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json<AuthResponse>({
        success: false,
        message: '创建用户失败，请稍后重试'
      }, { status: 500 });
    }

    // 为返回的用户数据添加is_admin字段
    const userWithAdmin = { ...newUser, is_admin: false, last_login: null };

    return NextResponse.json<AuthResponse>({
      success: true,
      message: '用户创建成功',
      user: userWithAdmin
    }, { status: 201 });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json<AuthResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
