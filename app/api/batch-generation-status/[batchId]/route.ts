import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { BatchGenerationStatus } from '@/types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ batchId: string }> }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({
        success: false,
        message: '认证令牌无效或已过期'
      }, { status: 401 });
    }

    const { batchId } = await params;

    // 获取批量任务信息
    const { data: task, error: taskError } = await supabaseAdmin
      .from('batch_generation_tasks')
      .select('*')
      .eq('id', batchId)
      .eq('user_id', payload.userId)
      .single();

    if (taskError || !task) {
      return NextResponse.json({
        success: false,
        message: '找不到指定的批量任务'
      }, { status: 404 });
    }

    // 获取批量结果信息
    const { data: results, error: resultsError } = await supabaseAdmin
      .from('batch_generation_results')
      .select('*')
      .eq('batch_task_id', batchId)
      .order('created_at', { ascending: true });

    if (resultsError) {
      console.error('获取批量结果失败:', resultsError);
      return NextResponse.json({
        success: false,
        message: '获取批量结果失败'
      }, { status: 500 });
    }

    // 计算总Token使用量
    const totalTokensUsed = results?.reduce((total, result) => {
      return total + (result.tokens_used || 0);
    }, 0) || 0;

    const response: BatchGenerationStatus = {
      success: true,
      task,
      results: results || [],
      totalTokensUsed
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('获取批量生成状态错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
