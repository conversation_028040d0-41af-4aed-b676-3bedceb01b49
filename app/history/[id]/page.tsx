'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { CommentRecord, User } from '@/types';

interface CommentDetailResponse {
  success: boolean;
  message: string;
  comment?: CommentRecord;
}

export default function CommentDetailPage() {
  const router = useRouter();
  const params = useParams();
  const [user, setUser] = useState<User | null>(null);
  const [comment, setComment] = useState<CommentRecord | null>(null);
  const [loading, setLoading] = useState(false);

  // 检查用户登录状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/login');
    }
  }, [router]);

  // 加载评语详情
  const loadCommentDetail = useCallback(async () => {
    if (!params.id) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/comments/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data: CommentDetailResponse = await response.json();

      if (data.success) {
        setComment(data.comment || null);
      } else {
        alert(data.message || '加载评语详情失败');
        router.push('/history');
      }
    } catch (error) {
      console.error('加载评语详情错误:', error);
      alert('加载评语详情失败，请稍后重试');
      router.push('/history');
    } finally {
      setLoading(false);
    }
  }, [params.id, router]);

  // 初始加载
  useEffect(() => {
    if (user && params.id) {
      loadCommentDetail();
    }
  }, [user, params.id, loadCommentDetail]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 退出登录
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-gray-900">评语详情</h1>
                <button
                  onClick={() => router.push('/history')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  返回列表
                </button>
              </div>
            </div>
          </div>
        </nav>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  if (!comment) {
    return (
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-gray-900">评语详情</h1>
                <button
                  onClick={() => router.push('/history')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  返回列表
                </button>
              </div>
            </div>
          </div>
        </nav>
        <div className="text-center py-8 text-gray-500">
          评语不存在或已被删除
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">评语详情</h1>
              <button
                onClick={() => router.push('/history')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                返回列表
              </button>
              <button
                onClick={() => router.push('/')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                返回首页
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">欢迎，{user.username}</span>
              {user.is_admin && (
                <button
                  onClick={() => router.push('/admin')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  管理后台
                </button>
              )}
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：学生信息 */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-medium text-gray-900">学生信息</h2>
              <div className="text-sm text-gray-500">
                生成时间：{formatDate(comment.created_at)}
              </div>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生姓名
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.studentName}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生性别
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.studentGender}
                  </div>
                </div>
              </div>



              {comment.student_info.subjectStrengths && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学科强项分析
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.subjectStrengths}
                  </div>
                </div>
              )}

              {comment.student_info.subjectWeaknesses && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学科薄弱点分析
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.subjectWeaknesses}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学习潜力评估
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.learningPotential}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    对学科的兴趣程度
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.subjectInterest}
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  课堂专注度
                </label>
                <div className="px-3 py-2 bg-gray-50 rounded-md">
                  {comment.student_info.classroomConcentration}
                </div>
              </div>

              {comment.student_info.talentsAndInterests && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    特长与兴趣
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.talentsAndInterests}
                  </div>
                </div>
              )}

              {comment.student_info.overallAssessment && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    教师总体评价
                  </label>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    {comment.student_info.overallAssessment}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：生成的评语 */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-medium text-gray-900">生成的评语</h2>
              <div className="text-sm text-gray-500">
                使用了 {comment.tokens_used} 个 Token
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                  {comment.generated_comment}
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => navigator.clipboard.writeText(comment.generated_comment)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  复制评语
                </button>
                <button
                  onClick={() => {
                    // 将学生信息填充到首页表单中
                    localStorage.setItem('prefillStudentInfo', JSON.stringify(comment.student_info));
                    router.push('/');
                  }}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                >
                  基于此信息重新生成
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
