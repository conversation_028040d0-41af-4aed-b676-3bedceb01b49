import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

// 客户端用的Supabase实例（使用anon key）
export const supabase = createClient(supabaseUrl, supabaseKey);

// 服务端用的Supabase实例（使用service key，拥有更高权限）
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// 数据库表名常量
export const TABLES = {
  USERS: 'users',
  TOKEN_USAGE: 'token_usage',
  COMMENTS: 'comments'
} as const;
