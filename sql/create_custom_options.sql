-- 创建自定义选项表
CREATE TABLE IF NOT EXISTS custom_options (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  field_name VARCHAR(100) NOT NULL,
  option_value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, field_name, option_value)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_custom_options_user_field ON custom_options(user_id, field_name);
CREATE INDEX IF NOT EXISTS idx_custom_options_user_id ON custom_options(user_id);

-- 添加注释
COMMENT ON TABLE custom_options IS '用户自定义选项表';
COMMENT ON COLUMN custom_options.user_id IS '用户ID';
COMMENT ON COLUMN custom_options.field_name IS '字段名称';
COMMENT ON COLUMN custom_options.option_value IS '选项值';

-- 插入默认选项数据
INSERT INTO custom_options (user_id, field_name, option_value) 
SELECT 
  u.id,
  'overallAssessment',
  option_value
FROM users u
CROSS JOIN (
  VALUES 
    ('该生表现优秀，各方面发展均衡'),
    ('该生学习态度端正，成绩稳步提升'),
    ('该生积极向上，具有良好的发展潜力'),
    ('该生在某些方面表现突出，需继续保持'),
    ('该生基础扎实，但需要在某些方面加强努力')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;

INSERT INTO custom_options (user_id, field_name, option_value) 
SELECT 
  u.id,
  'futureExpectations',
  option_value
FROM users u
CROSS JOIN (
  VALUES 
    ('希望能在学习上更加主动积极'),
    ('期待在各学科均衡发展'),
    ('希望能发挥特长，全面发展'),
    ('期望能提高自主学习能力'),
    ('希望能在团队合作中发挥更大作用')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;

INSERT INTO custom_options (user_id, field_name, option_value) 
SELECT 
  u.id,
  'improvementSuggestions',
  option_value
FROM users u
CROSS JOIN (
  VALUES 
    ('建议加强基础知识的巩固练习'),
    ('建议多参与课堂讨论，提高表达能力'),
    ('建议制定合理的学习计划并坚持执行'),
    ('建议多阅读课外书籍，拓宽知识面'),
    ('建议积极参与集体活动，培养团队精神')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;

INSERT INTO custom_options (user_id, field_name, option_value) 
SELECT 
  u.id,
  'subjectStrengths',
  option_value
FROM users u
CROSS JOIN (
  VALUES 
    ('语文阅读理解能力强'),
    ('数学逻辑思维清晰'),
    ('英语口语表达流利'),
    ('理科思维敏捷'),
    ('文科知识面广'),
    ('艺术天赋突出'),
    ('体育运动能力强')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;

INSERT INTO custom_options (user_id, field_name, option_value)
SELECT
  u.id,
  'subjectWeaknesses',
  option_value
FROM users u
CROSS JOIN (
  VALUES
    ('数学计算准确性有待提高'),
    ('语文写作表达需要加强'),
    ('英语词汇量需要扩大'),
    ('理科实验操作需要练习'),
    ('文科记忆背诵需要加强'),
    ('注意力集中度需要提升'),
    ('学习方法需要改进')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;

INSERT INTO custom_options (user_id, field_name, option_value)
SELECT
  u.id,
  'classPosition',
  option_value
FROM users u
CROSS JOIN (
  VALUES
    ('无职位'),
    ('班长'),
    ('副班长'),
    ('学习委员'),
    ('体育委员'),
    ('文艺委员'),
    ('生活委员'),
    ('纪律委员'),
    ('宣传委员'),
    ('组织委员'),
    ('科代表'),
    ('小组长')
) AS default_options(option_value)
ON CONFLICT DO NOTHING;
