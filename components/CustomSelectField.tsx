'use client';

import { useState, useEffect, useCallback } from 'react';
import OptionsManager from './OptionsManager';

interface CustomSelectFieldProps {
  label: string;
  fieldName: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

export default function CustomSelectField({
  label,
  fieldName,
  value,
  onChange,
  placeholder = "请选择...",
  required = false
}: CustomSelectFieldProps) {
  const [options, setOptions] = useState<string[]>([]);
  const [showManager, setShowManager] = useState(false);
  const [loading, setLoading] = useState(false);

  // 获取默认选项
  const getDefaultOptions = (fieldName: string): string[] => {
    switch (fieldName) {
      case 'overallAssessment':
        return [
          '该生表现优秀，各方面发展均衡',
          '该生学习态度端正，成绩稳步提升',
          '该生积极向上，具有良好的发展潜力',
          '该生在某些方面表现突出，需继续保持',
          '该生基础扎实，但需要在某些方面加强努力'
        ];
      case 'futureExpectations':
        return [
          '希望能在学习上更加主动积极',
          '期待在各学科均衡发展',
          '希望能发挥特长，全面发展',
          '期望能提高自主学习能力',
          '希望能在团队合作中发挥更大作用'
        ];
      case 'improvementSuggestions':
        return [
          '建议加强基础知识的巩固练习',
          '建议多参与课堂讨论，提高表达能力',
          '建议制定合理的学习计划并坚持执行',
          '建议多阅读课外书籍，拓宽知识面',
          '建议积极参与集体活动，培养团队精神'
        ];
      case 'subjectStrengths':
        return [
          '语文阅读理解能力强',
          '数学逻辑思维清晰',
          '英语口语表达流利',
          '理科思维敏捷',
          '文科知识面广',
          '艺术天赋突出',
          '体育运动能力强'
        ];
      case 'subjectWeaknesses':
        return [
          '数学计算准确性有待提高',
          '语文写作表达需要加强',
          '英语词汇量需要扩大',
          '理科实验操作需要练习',
          '文科记忆背诵需要加强',
          '注意力集中度需要提升',
          '学习方法需要改进'
        ];
      case 'classPosition':
        return [
          '无职位',
          '班长',
          '副班长',
          '学习委员',
          '体育委员',
          '文艺委员',
          '生活委员',
          '纪律委员',
          '宣传委员',
          '组织委员',
          '科代表',
          '小组长'
        ];
      default:
        return [];
    }
  };

  // 加载选项
  const loadOptions = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // 没有 token，使用默认选项
        const defaultOptions = getDefaultOptions(fieldName);
        setOptions(defaultOptions);
        return;
      }

      const response = await fetch(`/api/custom-options?fieldName=${fieldName}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setOptions(data.options || []);
      } else {
        // 如果获取失败（包括 token 验证失败或表不存在），使用默认选项
        if (data.useDefault) {
          console.log('使用默认选项:', data.message);
        } else {
          console.log('API 获取选项失败，使用默认选项:', data.message);
        }
        const defaultOptions = getDefaultOptions(fieldName);
        setOptions(defaultOptions);
      }
    } catch (error) {
      console.error('加载选项失败:', error);
      // 如果请求失败，使用默认选项
      const defaultOptions = getDefaultOptions(fieldName);
      setOptions(defaultOptions);
    } finally {
      setLoading(false);
    }
  }, [fieldName]);

  useEffect(() => {
    loadOptions();
  }, [loadOptions]);

  return (
    <div>
      <div className="flex items-center justify-between mb-1">
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <button
          type="button"
          onClick={() => setShowManager(true)}
          className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
          title="管理选项"
        >
          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          管理选项
        </button>
      </div>

      <div className="relative">
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
          disabled={loading}
        >
          <option value="">{loading ? '加载中...' : placeholder}</option>
          {options.map((option, index) => (
            <option key={index} value={option}>
              {option}
            </option>
          ))}
        </select>
        
        {/* 自定义下拉箭头 */}
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* 选项管理弹窗 */}
      <OptionsManager
        fieldName={fieldName}
        fieldLabel={label}
        value={value}
        onChange={(newValue) => {
          onChange(newValue);
        }}
        isOpen={showManager}
        onClose={() => setShowManager(false)}
        onOptionsChange={loadOptions} // 当选项变更时重新加载
      />
    </div>
  );
}
