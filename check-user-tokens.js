// 检查用户 Token 使用情况的脚本
// 使用方法: node check-user-tokens.js

const checkUserTokens = async () => {
  const baseUrl = 'http://localhost:3000'; // 根据您的实际端口调整
  
  // 管理员登录信息
  const adminUser = {
    username: 'cbun', // 请填入管理员用户名
    password: 'your_admin_password_here' // 请填入管理员密码
  };

  try {
    console.log('🔐 正在登录管理员账户...');
    
    // 1. 管理员登录
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(adminUser)
    });

    if (!loginResponse.ok) {
      throw new Error(`登录失败: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const adminToken = loginData.token;
    console.log('✅ 管理员登录成功');

    // 2. 获取所有用户列表
    console.log('📋 获取用户列表...');
    const usersResponse = await fetch(`${baseUrl}/api/admin/users`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });

    if (!usersResponse.ok) {
      throw new Error(`获取用户列表失败: ${usersResponse.status}`);
    }

    const usersData = await usersResponse.json();
    if (!usersData.success) {
      throw new Error(`获取用户列表失败: ${usersData.message}`);
    }

    const users = usersData.data;
    console.log(`📊 找到 ${users.length} 个用户`);

    // 3. 检查每个用户的 Token 使用情况
    for (const user of users) {
      console.log(`\n👤 检查用户: ${user.username} (ID: ${user.id})`);
      console.log(`   Token 限制: ${user.token_limit === null ? '无限制' : user.token_limit}`);
      console.log(`   账户状态: ${user.is_active ? '活跃' : '禁用'}`);

      // 获取用户详细的 Token 使用统计
      const tokenStatsResponse = await fetch(`${baseUrl}/api/admin/users/${user.id}/tokens`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (tokenStatsResponse.ok) {
        const tokenStatsData = await tokenStatsResponse.json();
        if (tokenStatsData.success) {
          const stats = tokenStatsData.data;
          console.log(`   总使用量: ${stats.total_tokens_used}`);
          console.log(`   使用百分比: ${stats.usage_percentage}%`);
          console.log(`   最近使用记录数: ${stats.recent_usage.length}`);
          
          if (stats.recent_usage.length > 0) {
            console.log(`   最近一次使用: ${stats.recent_usage[0].created_at} (${stats.recent_usage[0].tokens_used} tokens)`);
          }
        } else {
          console.log(`   ❌ 获取 Token 统计失败: ${tokenStatsData.message}`);
        }
      } else {
        console.log(`   ❌ 获取 Token 统计失败: HTTP ${tokenStatsResponse.status}`);
      }
    }

    // 4. 特别检查 test2 用户（如果存在）
    const test2User = users.find(u => u.username === 'test2');
    if (test2User) {
      console.log(`\n🔍 特别检查 test2 用户的详细情况:`);
      console.log(`   用户ID: ${test2User.id}`);
      console.log(`   Token 限制: ${test2User.token_limit}`);
      console.log(`   是否活跃: ${test2User.is_active}`);
      
      // 获取 Token 限制设置
      const tokenLimitResponse = await fetch(`${baseUrl}/api/admin/users/${test2User.id}/token-limit`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (tokenLimitResponse.ok) {
        const tokenLimitData = await tokenLimitResponse.json();
        if (tokenLimitData.success) {
          console.log(`   数据库中的 Token 限制: ${tokenLimitData.data.tokenLimit}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message);
  }
};

// 运行检查
checkUserTokens();
