# 学生评语生成系统

基于 AI 的学生评语生成和管理系统，集成了完整的后台管理功能和用户认证系统。

## 功能特性

### 🎯 核心功能
- **AI 评语生成**: 基于 Deepseek API 的智能学生评语生成
- **用户认证系统**: 完整的用户登录、注册和权限管理
- **后台管理系统**: 管理员可以管理用户账号和查看使用统计
- **Token 使用追踪**: 详细记录每个用户的 API 调用和 Token 消耗

### 🔐 安全特性
- 基于 JWT 的用户认证
- 管理员账密环境变量控制
- Supabase 行级安全策略 (RLS)
- 密码加密存储

### 📊 管理功能
- 用户账号管理（创建、编辑、删除、禁用）
- Token 使用统计和趋势分析
- 用户使用排行和详细记录
- 实时数据仪表板

## 技术栈

- **后端**: nextjs
- **数据库**: Supabase (PostgreSQL)
- **前端**: HTML + Tailwind CSS + nextjs
- **认证**: JWT + Passlib
- **AI API**: Deepseek API