{"name": "easy_comments", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.49.10", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}