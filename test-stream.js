// 测试流式输出功能
const baseUrl = 'http://localhost:3000';

// 测试用的学生信息
const testStudentInfo = {
  studentName: '张三',
  studentGender: '男',
  subjectStrengths: '数学逻辑思维强',
  subjectWeaknesses: '语文表达需要提升',
  learningPotential: '学习能力稳定',
  subjectInterest: '一般',
  classroomConcentration: '大部分时间专注',
  homeworkCompletion: '大部分按时完成',
  learningProactiveness: '完成要求任务',
  disciplineCompliance: '基本遵守',
  attitudeTowardsOthers: '基本有礼貌',
  responsibility: '能完成分配任务',
  talentsAndInterests: '喜欢篮球运动',
  classPosition: '无职位',
  awards: '校三好学生',
  overallAssessment: '表现良好',
  futureExpectations: '希望继续进步',
  improvementSuggestions: '加强语文学习',
  commentPerspective: '你',
  commentTone: '温和亲切',
  wordCountRange: '200-300字'
};

async function testStreamGeneration() {
  console.log('🧪 开始测试流式评语生成...');

  try {
    // 1. 先登录获取token
    console.log('📝 正在登录...');
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'cbun',
        password: 'cbun'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.error('❌ 登录失败:', loginData.message);
      return;
    }

    const token = loginData.token;
    console.log('✅ 登录成功');

    // 2. 测试流式生成评语
    console.log('🚀 开始流式生成评语...');
    const response = await fetch(`${baseUrl}/api/generate-comment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ studentInfo: testStudentInfo })
    });

    if (!response.ok) {
      console.error('❌ 请求失败:', response.status, response.statusText);
      return;
    }

    // 检查是否是流式响应
    const contentType = response.headers.get('content-type');
    console.log('📄 响应类型:', contentType);

    if (contentType?.includes('text/event-stream')) {
      console.log('🌊 检测到流式响应，开始读取...');
      
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        console.error('❌ 无法获取响应流读取器');
        return;
      }

      let accumulatedContent = '';
      let chunkCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('✅ 流读取完成');
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);
        console.log(`📦 收到第 ${chunkCount} 个数据块:`, chunk.substring(0, 100) + '...');

        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.error) {
                console.error('❌ 流中收到错误:', parsed.error);
                return;
              }
              
              if (parsed.type === 'content' && parsed.content) {
                accumulatedContent += parsed.content;
                process.stdout.write(parsed.content); // 实时显示内容
              } else if (parsed.type === 'done') {
                console.log('\n✅ 生成完成!');
                console.log('🔢 使用的 Token 数量:', parsed.tokensUsed);
                console.log('📝 完整评语:');
                console.log('---');
                console.log(accumulatedContent);
                console.log('---');
                return;
              }
            } catch (e) {
              console.warn('⚠️ 解析流数据失败:', e.message);
            }
          }
        }
      }
    } else {
      console.log('📄 非流式响应，尝试解析JSON...');
      const data = await response.json();
      console.log('📝 响应数据:', data);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testStreamGeneration();
